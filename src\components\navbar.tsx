"use client"
import Link from 'next/link'
import Image from 'next/image'
import { useState } from 'react'
import { Menu, X } from 'lucide-react'

export default function Navbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <nav className="fixed top-4 left-1/2 -translate-x-1/2 z-50 w-[95%] md:w-auto">
      {/* Fluid Background Effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-[#aab2d0]/10 via-[#9b99b5]/5 to-[#aab2d0]/10 rounded-full blur-xl" />

      {/* Particle Effects */}
      <div className="absolute inset-0 overflow-hidden rounded-full">
        {[...Array(8)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-[#aab2d0] rounded-full opacity-40 animate-pulse"
            style={{
              left: `${10 + (i * 12)}%`,
              top: `${20 + Math.sin(i) * 60}%`,
              animationDelay: `${i * 0.5}s`,
              animationDuration: `${2 + Math.random()}s`,
            }}
          />
        ))}
      </div>

      <div className="relative bg-black/20 backdrop-blur-xl border border-white/10 rounded-full flex items-center justify-between md:justify-start shadow-2xl">
        <div className="flex items-center">
          <Link href="/" className="flex items-center px-4 py-3 group">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] rounded-full blur-md opacity-50 group-hover:opacity-75 transition-opacity" />
              <Image
                src="/window.svg"
                alt="stun"
                width={32}
                height={32}
                className="relative rounded-full border border-white/20"
              />
            </div>
            <span className="ml-3 text-lg font-bold bg-gradient-to-r from-white to-[#aab2d0] bg-clip-text text-transparent">
              stun
            </span>
          </Link>

          <div className="w-[1px] h-6 bg-gradient-to-b from-transparent via-white/20 to-transparent mx-2" />

          <div className="hidden md:flex items-center">
            <Link href="/commands" className="text-sm text-white/70 px-4 py-2 transition-all duration-300 hover:text-white relative group">
              Commands
              <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] group-hover:w-3/4 transition-all duration-300" />
            </Link>
            <div className="w-[1px] h-4 bg-white/10 mx-1" />
            <Link href="/faq" className="text-sm text-white/70 px-4 py-2 transition-all duration-300 hover:text-white relative group">
              FAQ
              <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] group-hover:w-3/4 transition-all duration-300" />
            </Link>
            <div className="w-[1px] h-4 bg-white/10 mx-1" />
            <Link href="/premium" className="text-sm text-white/70 px-4 py-2 transition-all duration-300 hover:text-white relative group">
              Premium
              <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] group-hover:w-3/4 transition-all duration-300" />
            </Link>
            <div className="w-[1px] h-4 bg-white/10 mx-1" />
            <Link href="https://discord.gg/heistbot" className="text-sm text-white/70 px-4 py-2 transition-all duration-300 hover:text-white relative group">
              Discord
              <div className="absolute -bottom-1 left-1/2 -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] group-hover:w-3/4 transition-all duration-300" />
            </Link>
          </div>
        </div>

        <button
          className="md:hidden px-4 py-3 text-white/70 hover:text-white transition-colors"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
        </button>
      </div>

      <div className={`md:hidden absolute top-full left-0 right-0 mt-2 bg-black/20 backdrop-blur-xl border border-white/10 rounded-xl overflow-hidden transition-all duration-300 ${
        mobileMenuOpen ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4 pointer-events-none'
      }`}>
        <div className="flex flex-col divide-y divide-white/10">
          <Link href="/commands" className="text-sm text-white/70 px-4 py-3 hover:bg-white/5 hover:text-white transition-all duration-200">
            Commands
          </Link>
          <Link href="/faq" className="text-sm text-white/70 px-4 py-3 hover:bg-white/5 hover:text-white transition-all duration-200">
            FAQ
          </Link>
          <Link href="/premium" className="text-sm text-white/70 px-4 py-3 hover:bg-white/5 hover:text-white transition-all duration-200">
            Premium
          </Link>
          <Link href="https://discord.gg/heistbot" className="text-sm text-white/70 px-4 py-3 hover:bg-white/5 hover:text-white transition-all duration-200">
            Discord
          </Link>
        </div>
      </div>
    </nav>
  )
}

