"use client"
import Link from 'next/link'
import Image from 'next/image'
import { useState } from 'react'
import { Menu, X } from 'lucide-react'

export default function Navbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  return (
    <nav className="w-full px-6 py-4 bg-[#1a1a2e]/80 backdrop-blur-sm border-b border-white/5">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-3 group">
          <div className="relative">
            <Image
              src="/window.svg"
              alt="stun"
              width={32}
              height={32}
              className="rounded-lg"
            />
          </div>
          <span className="text-xl font-bold text-white">
            stun
          </span>
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center gap-8">
          <Link href="/commands" className="text-white/70 hover:text-white transition-colors text-sm font-medium">
            Commands
          </Link>
          <Link href="/faq" className="text-white/70 hover:text-white transition-colors text-sm font-medium">
            FAQ
          </Link>
          <Link href="/premium" className="text-white/70 hover:text-white transition-colors text-sm font-medium">
            Premium
          </Link>
          <Link href="https://discord.gg/heistbot" className="text-white/70 hover:text-white transition-colors text-sm font-medium">
            Discord
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-white/70 hover:text-white transition-colors"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>
      </div>

      {/* Mobile Menu */}
      <div className={`md:hidden mt-4 transition-all duration-300 ${
        mobileMenuOpen ? 'opacity-100 max-h-48' : 'opacity-0 max-h-0 overflow-hidden'
      }`}>
        <div className="flex flex-col gap-2 pt-4 border-t border-white/10">
          <Link href="/commands" className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium">
            Commands
          </Link>
          <Link href="/faq" className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium">
            FAQ
          </Link>
          <Link href="/premium" className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium">
            Premium
          </Link>
          <Link href="https://discord.gg/heistbot" className="text-white/70 hover:text-white transition-colors py-2 text-sm font-medium">
            Discord
          </Link>
        </div>
      </div>
    </nav>
  )
}

