@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --font-sans: "Inter", system-ui, -apple-system, sans-serif;
    --background: 0 0% 0%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 220 70% 65%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

body {
  background-color: theme("colors.background");
  color: theme("colors.foreground");
  font-feature-settings:
    "rlig" 1,
    "calt" 1;
}

.radix-themes {
  --color-background: transparent !important;
  --color-panel: transparent !important;
}

.rt-Button[data-disabled] .rt-Spinner {
  --spinner-opacity: 1;
  color: var(--accent-12);
}

.rt-Button[data-disabled] {
  opacity: 0.9;
  cursor: not-allowed;
}

.glass-panel {
  background: rgba(24, 24, 27, 0.5);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(39, 39, 42, 0.2);
  border-radius: 1rem;
}

.dynamic-island {
  background: rgba(24, 24, 27, 0.95);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(39, 39, 42, 0.3);
  box-shadow: theme("boxShadow.island");
  border-radius: 32px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dynamic-island:hover {
  border-color: rgba(39, 39, 42, 0.5);
}

.text-gradient {
  background: linear-gradient(
    to right bottom,
    rgb(250, 250, 250) 30%,
    rgba(170, 178, 208, 0.839)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-card {
  @apply bg-white/[0.05] backdrop-blur-lg border border-white/[0.1] shadow-black/10;
}

.ease-spring {
  transition-timing-function: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes scroll {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-100% - 1rem));
  }
}

.animate-scroll {
  animation: scroll var(--animation-duration, 20s)
    var(--animation-direction, forwards) linear infinite;
}

@keyframes aurora {
  from {
    background-position:
      50% 50%,
      50% 50%;
  }
  to {
    background-position:
      350% 50%,
      350% 50%;
  }
}

.animate-aurora {
  animation: aurora 60s linear infinite;
}

.aurora-text {
  --aurora-blur: 180px;
  --aurora-opacity: 0.3;
  --aurora-speed: 10s;
  background-image: linear-gradient(
    45deg,
    rgba(103, 145, 229, var(--aurora-opacity)),
    rgba(70, 113, 198, var(--aurora-opacity)),
    rgba(45, 67, 116, var(--aurora-opacity))
  );
  filter: blur(var(--aurora-blur));
  animation: aurora var(--aurora-speed) linear infinite;
  background-size: 200% auto;
}

@keyframes ripple {
  0% {
    transform: scale(0.5);
    opacity: 0.8;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ripple {
  animation: ripple 2s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Enhanced Background Animations */
@keyframes float-slow {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-15px) translateX(5px);
  }
  50% {
    transform: translateY(-30px) translateX(0px);
  }
  75% {
    transform: translateY(-15px) translateX(-5px);
  }
}

@keyframes float-medium {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-20px) translateX(10px);
  }
  66% {
    transform: translateY(-10px) translateX(-8px);
  }
}

@keyframes float-fast {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  50% {
    transform: translateY(-25px) translateX(15px);
  }
}

@keyframes rotate-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate-reverse {
  from {
    transform: rotate(360deg);
  }
  to {
    transform: rotate(0deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--primary-accent), 0 0 10px var(--primary-accent), 0 0 15px var(--primary-accent);
    opacity: 0.3;
  }
  50% {
    box-shadow: 0 0 10px var(--primary-accent), 0 0 20px var(--primary-accent), 0 0 30px var(--primary-accent);
    opacity: 0.6;
  }
}

@keyframes pulse-scale {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes grid-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(60px, 60px);
  }
}

@keyframes particle-float-0 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.4;
  }
  25% {
    transform: translateY(-100px) translateX(50px);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-200px) translateX(0px);
    opacity: 0.6;
  }
  75% {
    transform: translateY(-150px) translateX(-30px);
    opacity: 0.9;
  }
}

@keyframes particle-float-1 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.3;
  }
  33% {
    transform: translateY(-80px) translateX(-40px);
    opacity: 0.7;
  }
  66% {
    transform: translateY(-160px) translateX(20px);
    opacity: 0.5;
  }
}

@keyframes particle-float-2 {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.5;
  }
  40% {
    transform: translateY(-120px) translateX(60px);
    opacity: 0.8;
  }
  80% {
    transform: translateY(-240px) translateX(-10px);
    opacity: 0.4;
  }
}

@keyframes wave-move {
  0%, 100% {
    transform: translateX(0%) translateY(0%);
  }
  25% {
    transform: translateX(5%) translateY(-2%);
  }
  50% {
    transform: translateX(-3%) translateY(3%);
  }
  75% {
    transform: translateX(2%) translateY(-1%);
  }
}

@keyframes morph-blob {
  0%, 100% {
    border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
  }
  25% {
    border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
  }
  50% {
    border-radius: 50% 60% 30% 60% / 60% 30% 60% 40%;
  }
  75% {
    border-radius: 60% 40% 60% 30% / 30% 70% 40% 60%;
  }
}

@keyframes morph-blob-reverse {
  0%, 100% {
    border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
  }
  25% {
    border-radius: 70% 30% 30% 70% / 70% 70% 30% 30%;
  }
  50% {
    border-radius: 40% 60% 60% 40% / 60% 40% 40% 60%;
  }
  75% {
    border-radius: 60% 40% 40% 60% / 40% 60% 60% 40%;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Enhanced Background Animation Classes */
.animate-float-slow {
  animation: float-slow 8s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 6s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 4s ease-in-out infinite;
}

.animate-rotate-slow {
  animation: rotate-slow 20s linear infinite;
}

.animate-rotate-reverse {
  animation: rotate-reverse 15s linear infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 3s ease-in-out infinite;
}

.animate-pulse-scale {
  animation: pulse-scale 5s ease-in-out infinite;
}

.animate-grid-move {
  animation: grid-move 20s linear infinite;
}

.animate-wave-move {
  animation: wave-move 25s ease-in-out infinite;
}

.animate-morph-blob {
  animation: morph-blob 12s ease-in-out infinite;
}

.animate-morph-blob-reverse {
  animation: morph-blob-reverse 10s ease-in-out infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.mask-fade {
  -webkit-mask-image: linear-gradient(
    to right,
    transparent,
    white 20%,
    white 80%,
    transparent
  );
  mask-image: linear-gradient(
    to right,
    transparent,
    white 20%,
    white 80%,
    transparent
  );
}
