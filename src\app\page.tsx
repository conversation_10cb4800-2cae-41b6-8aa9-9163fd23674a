import React from "react";
import Image from "next/image";
import Link from "next/link";
import AnimatedBackground from "@/components/animated-background";

export default function Home() {
  // Static stats for now to avoid client-side complexity
  const statList = [
    { label: "Daily Users", value: 64941 },
    { label: "Commands", value: 112 },
    { label: "Servers", value: 9 },
    { label: "DB Docs", value: 20996 },
  ];

  return (
    <div className="min-h-screen w-full relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#0f0f23] via-[#1a1a2e] to-[#16213e]">
        <AnimatedBackground />
      </div>

      {/* Unique Navbar */}
      <nav className="relative z-50 w-full px-8 py-6">
        <div className="flex items-center justify-between">
          {/* Logo Section */}
          <div className="flex items-center gap-3 group">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] rounded-full blur-md opacity-50 group-hover:opacity-75 transition-opacity" />
              <Image
                src="/window.svg"
                alt="stun logo"
                width={40}
                height={40}
                className="relative rounded-full border border-white/20"
              />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-white to-[#aab2d0] bg-clip-text text-transparent">
              stun
            </span>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center gap-8">
            <Link href="/commands" className="text-white/70 hover:text-white transition-all duration-300 relative group">
              Commands
              <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] group-hover:w-full transition-all duration-300" />
            </Link>
            <Link href="/faq" className="text-white/70 hover:text-white transition-all duration-300 relative group">
              FAQ
              <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] group-hover:w-full transition-all duration-300" />
            </Link>
            <Link href="/premium" className="text-white/70 hover:text-white transition-all duration-300 relative group">
              Premium
              <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] group-hover:w-full transition-all duration-300" />
            </Link>
            <Link href="https://discord.gg/heistbot" className="text-white/70 hover:text-white transition-all duration-300 relative group">
              Discord
              <div className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] group-hover:w-full transition-all duration-300" />
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden text-white">
            <svg width="24" height="24" fill="none" viewBox="0 0 24 24">
              <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16"/>
            </svg>
          </button>
        </div>
      </nav>

      {/* Hero Section */}
      <main className="relative z-10 flex flex-col items-center justify-center min-h-[80vh] px-4">
        <div className="max-w-6xl w-full grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-6xl lg:text-7xl font-extrabold">
                <span className="bg-gradient-to-r from-white via-[#aab2d0] to-[#9b99b5] bg-clip-text text-transparent">
                  stun
                </span>
              </h1>
              <p className="text-xl text-white/80 leading-relaxed max-w-lg">
                The professional Discord bot for{" "}
                <span className="font-semibold text-[#aab2d0]">effective moderation</span>{" "}
                and{" "}
                <span className="font-semibold text-[#aab2d0]">server management</span>.
              </p>
              <p className="text-white/60">
                Trusted by{" "}
                <span className="font-bold text-white">{statList[0].value.toLocaleString()}</span>{" "}
                users across{" "}
                <span className="font-bold text-white">{statList[2].value}</span>{" "}
                servers with{" "}
                <span className="font-bold text-white">{statList[1].value}</span>{" "}
                commands.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                href="#"
                className="group relative px-8 py-4 bg-gradient-to-r from-[#aab2d0] to-[#9b99b5] rounded-xl font-semibold text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-2"
              >
                <span>Add to Discord</span>
                <svg width="20" height="20" fill="none" viewBox="0 0 24 24" className="group-hover:translate-x-1 transition-transform">
                  <path fill="currentColor" d="M14.7 5.3a1 1 0 0 1 1.4 0l5 5a1 1 0 0 1 0 1.4l-5 5a1 1 0 1 1-1.4-1.4l2.3-2.3H5a1 1 0 1 1 0-2h12.01l-2.3-2.3a1 1 0 0 1 0-1.4Z"/>
                </svg>
              </Link>
              <Link
                href="/commands"
                className="px-8 py-4 bg-white/5 backdrop-blur-xl border border-white/20 rounded-xl font-semibold text-white hover:bg-white/10 transition-all duration-300 flex items-center justify-center"
              >
                View Commands
              </Link>
            </div>
          </div>

          {/* Right Side - Avatar/Visual */}
          <div className="relative flex justify-center lg:justify-end">
            <div className="relative">
              {/* Glow Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-[#aab2d0]/30 to-[#9b99b5]/30 rounded-3xl blur-2xl scale-110" />

              {/* Avatar Container */}
              <div className="relative bg-white/5 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-[#aab2d0]/20 to-[#9b99b5]/20 rounded-2xl blur-xl" />
                  <Image
                    src="/window.svg"
                    alt="stun avatar"
                    width={200}
                    height={200}
                    className="relative rounded-2xl border border-white/30"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20 w-full max-w-6xl">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {statList.map((stat, index) => (
              <div
                key={stat.label}
                className="group relative bg-white/5 backdrop-blur-xl border border-white/20 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300 transform hover:scale-105"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-[#aab2d0]/10 to-[#9b99b5]/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                <div className="relative text-center">
                  <div className="text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white to-[#aab2d0] bg-clip-text text-transparent mb-2">
                    {stat.value.toLocaleString()}
                  </div>
                  <div className="text-white/60 text-sm font-medium">
                    {stat.label}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </main>

      {/* Status Indicators */}
      <div className="relative z-10 px-8 pb-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-wrap justify-center gap-4 text-sm text-white/50">
            <div className="flex items-center gap-2 bg-white/5 backdrop-blur-xl border border-white/10 rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span>stun.best - emojis [V2]</span>
            </div>
            <div className="flex items-center gap-2 bg-white/5 backdrop-blur-xl border border-white/10 rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span>stun.dev</span>
            </div>
            <div className="flex items-center gap-2 bg-white/5 backdrop-blur-xl border border-white/10 rounded-full px-4 py-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
              <span>Emoji Backup</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

