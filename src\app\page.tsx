"use client"
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";

interface ShardData {
  shard_id: number;
  is_ready: boolean;
  server_count: number;
  member_count: number;
  uptime: number;
  latency: number;
  last_updated: string;
}

interface HistoryData {
  guilds: number;
  users: number;
  ping: number;
  timestamp: string;
}

export default function Home() {
  const [shardData, setShardData] = useState<ShardData[]>([]);
  const [historyData, setHistoryData] = useState<HistoryData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        // Fetch shard data
        const shardResponse = await fetch("https://api.stun.lat/shards");
        if (!shardResponse.ok) throw new Error("Failed to fetch shard data");
        const shards = await shardResponse.json();
        setShardData(shards);

        // Fetch history data for additional stats
        const historyResponse = await fetch("https://api.stun.lat/history");
        if (!historyResponse.ok) throw new Error("Failed to fetch history data");
        const history = await historyResponse.json();
        setHistoryData(history);

      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to fetch data");
        // Fallback data
        setShardData([{
          shard_id: 0,
          is_ready: true,
          server_count: 7,
          member_count: 75,
          uptime: 360,
          latency: 25,
          last_updated: new Date().toISOString()
        }]);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  // Calculate total stats from shard data
  const totalServers = shardData.reduce((sum, shard) => sum + shard.server_count, 0);
  const totalMembers = shardData.reduce((sum, shard) => sum + shard.member_count, 0);
  const totalShards = shardData.length;
  const avgLatency = shardData.length > 0
    ? Math.round(shardData.reduce((sum, shard) => sum + shard.latency, 0) / shardData.length)
    : 0;

  return (
    <div className="min-h-screen w-full relative">
      {/* Simple Gradient Mesh Background */}
      <div className="absolute inset-0">
        {/* Main gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#0f0f23] via-[#1a1a2e] to-[#16213e]" />

        {/* Subtle gradient mesh overlay similar to reference */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute bottom-0 left-0 w-full h-1/2 bg-gradient-to-t from-[#aab2d0]/20 via-[#9b99b5]/10 to-transparent" />
          <div className="absolute bottom-0 right-0 w-1/2 h-full bg-gradient-to-l from-[#9b99b5]/15 via-transparent to-transparent" />
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 pt-20 pb-16">
        <div className="max-w-6xl mx-auto px-6">
          {/* Hero Section */}
          <div className="flex flex-col lg:flex-row items-center justify-between gap-12 mb-20">
            {/* Left Content */}
            <div className="flex-1 text-center lg:text-left">
              <h1 className="text-6xl lg:text-7xl font-bold text-white mb-6">
                stun
              </h1>
              <p className="text-lg text-white/80 mb-2 max-w-md">
                The professional Discord bot for{" "}
                <span className="font-semibold text-white">effective moderation</span>{" "}
                and{" "}
                <span className="font-semibold text-white">server management</span>.
              </p>
              <p className="text-white/60 mb-8 max-w-md">
                Trusted by{" "}
                <span className="font-bold text-white">
                  {loading ? "..." : totalMembers.toLocaleString()}
                </span>{" "}
                users across{" "}
                <span className="font-bold text-white">
                  {loading ? "..." : totalServers}
                </span>{" "}
                servers with{" "}
                <span className="font-bold text-white">112</span>{" "}
                commands.
              </p>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link
                  href="#"
                  className="bg-[#aab2d0] hover:bg-[#9b99b5] text-white font-semibold px-6 py-3 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  Add to Discord
                  <svg width="18" height="18" fill="none" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M14.7 5.3a1 1 0 0 1 1.4 0l5 5a1 1 0 0 1 0 1.4l-5 5a1 1 0 1 1-1.4-1.4l2.3-2.3H5a1 1 0 1 1 0-2h12.01l-2.3-2.3a1 1 0 0 1 0-1.4Z"/>
                  </svg>
                </Link>
                <Link
                  href="/commands"
                  className="bg-white/10 hover:bg-white/20 text-white font-semibold px-6 py-3 rounded-lg transition-colors"
                >
                  View Commands
                </Link>
              </div>
            </div>

            {/* Right Avatar */}
            <div className="flex-shrink-0">
              <div className="relative">
                <div className="w-64 h-64 lg:w-80 lg:h-80 rounded-2xl overflow-hidden bg-gradient-to-br from-[#aab2d0]/20 to-[#9b99b5]/20 backdrop-blur-sm border border-white/10">
                  <Image
                    src="/window.svg"
                    alt="stun avatar"
                    width={320}
                    height={320}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Stats Section - Horizontal Row like reference */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-[#aab2d0] mb-2">
                {loading ? "..." : totalMembers.toLocaleString()}
              </div>
              <div className="text-white/60 text-sm uppercase tracking-wide">
                Daily Users
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-[#9b99b5] mb-2">
                {loading ? "..." : totalServers}
              </div>
              <div className="text-white/60 text-sm uppercase tracking-wide">
                Guilds
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-[#aab2d0] mb-2">
                {loading ? "..." : totalShards}
              </div>
              <div className="text-white/60 text-sm uppercase tracking-wide">
                Shards
              </div>
            </div>

            <div className="text-center">
              <div className="text-3xl lg:text-4xl font-bold text-[#9b99b5] mb-2">
                {loading ? "..." : `${avgLatency}ms`}
              </div>
              <div className="text-white/60 text-sm uppercase tracking-wide">
                Ping
              </div>
            </div>
          </div>

          {/* Status Indicators */}
          {!loading && shardData.length > 0 && (
            <div className="mt-16 flex flex-wrap justify-center gap-4">
              {shardData.map((shard) => (
                <div
                  key={shard.shard_id}
                  className="flex items-center gap-2 bg-white/5 backdrop-blur-sm border border-white/10 rounded-full px-4 py-2 text-sm"
                >
                  <div className={`w-2 h-2 rounded-full ${
                    shard.is_ready ? 'bg-green-400' : 'bg-red-400'
                  } animate-pulse`} />
                  <span className="text-white/70">
                    shard {shard.shard_id} - {shard.server_count} guilds
                  </span>
                </div>
              ))}
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="mt-8 text-center">
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-red-400 text-sm">
                  {error}
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

